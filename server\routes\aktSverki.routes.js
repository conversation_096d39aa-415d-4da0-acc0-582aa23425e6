// server/routes/aktSverki.routes.js
const { Router } = require('express');
const { viewAktSverkiHTML } = require('../controllers/aktSverkiPreview.controller');
const { generateAktSverkiPDF } = require('../controllers/aktSverkiPdf.controller');

const router = Router();

// GET /api/html/akt-sverki — вернёт уже скомпилированную HTML-страницу
router.get('/akt-sverki', viewAktSverkiHTML);

module.exports = router;
