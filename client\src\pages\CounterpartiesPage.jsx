// src/pages/CounterpartiesPage.jsx
import React, { useEffect, useState } from "react";
import api from "../services/api";
import {
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@mui/material";

function CounterpartiesPage() {
  const [counterparties, setCounterparties] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editableCounterparty, setEditableCounterparty] = useState({
    name: "",
    inn: "",
    kpp: "",
    address: "",
    legal_address: "",
    contract: "",
    key: "",
  });

  useEffect(() => {
    loadCounterparties();
  }, []);

  const loadCounterparties = async () => {
    try {
      const res = await api.get("/counterparties");
      setCounterparties(res.data);
    } catch (err) {
      console.error("Failed to fetch counterparties:", err);
    }
  };

  const handleOpenDialog = (counterparty = null) => {
    if (counterparty) {
      setEditableCounterparty(counterparty);
    } else {
      setEditableCounterparty({
        name: "",
        inn: "",
        kpp: "",
        address: "",
        legal_address: "",
        contract: "",
        key: "",
        note: "",
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSave = async () => {
    try {
      if (editableCounterparty.id) {
        // Update
        await api.put(
          `/counterparties/${editableCounterparty.id}`,
          editableCounterparty
        );
      } else {
        // Create
        await api.post("/counterparties", editableCounterparty);
      }
      handleCloseDialog();
      loadCounterparties();
    } catch (err) {
      console.error("Failed to save counterparty:", err);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Удалить контрагента?")) return;
    try {
      await api.delete(`/counterparties/${id}`);
      loadCounterparties();
    } catch (err) {
      console.error("Failed to delete counterparty:", err);
    }
  };

  return (
    <div>
      <Typography variant="h5" gutterBottom>
        Контрагенты
      </Typography>

      <Button
        variant="contained"
        onClick={() => handleOpenDialog()}
        sx={{ mb: 2 }}
      >
        Добавить контрагента
      </Button>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Ключ</TableCell>
            <TableCell>Название</TableCell>
            <TableCell>ИНН</TableCell>
            <TableCell>КПП</TableCell>
            <TableCell>Адрес</TableCell>
            <TableCell>Адрес юр лица</TableCell>
            <TableCell>Договор</TableCell>
            <TableCell>Примечание</TableCell>
            <TableCell>Действия</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {counterparties.map((ct) => (
            <TableRow key={ct.id}>
              <TableCell>{ct.id}</TableCell>
              <TableCell>{ct.key}</TableCell>
              <TableCell>{ct.name}</TableCell>
              <TableCell>{ct.inn}</TableCell>
              <TableCell>{ct.kpp}</TableCell>
              <TableCell>{ct.address}</TableCell>
              <TableCell>{ct.legal_address}</TableCell>
              <TableCell>{ct.contract}</TableCell>
              <TableCell>{ct.note}</TableCell>
              <TableCell>
                <Button size="small" onClick={() => handleOpenDialog(ct)}>
                  Редактировать
                </Button>
                <Button
                  size="small"
                  color="error"
                  onClick={() => handleDelete(ct.id)}
                >
                  Удалить
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Диалоговое окно */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>
          {editableCounterparty.id
            ? "Редактировать контрагента"
            : "Добавить контрагента"}
        </DialogTitle>
        <DialogContent>
          <TextField
            label="Название"
            fullWidth
            margin="normal"
            value={editableCounterparty.name}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                name: e.target.value,
              })
            }
          />
          <TextField
            label="ИНН"
            fullWidth
            margin="normal"
            value={editableCounterparty.inn}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                inn: e.target.value,
              })
            }
          />
          <TextField
            label="КПП"
            fullWidth
            margin="normal"
            value={editableCounterparty.kpp}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                kpp: e.target.value,
              })
            }
          />
          <TextField
            label="Адрес"
            fullWidth
            margin="normal"
            value={editableCounterparty.address}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                address: e.target.value,
              })
            }
          />
          <TextField
            label="Юридический адрес"
            fullWidth
            margin="normal"
            value={editableCounterparty.legal_address}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                legal_address: e.target.value,
              })
            }
          />
          <TextField
            label="Договор"
            fullWidth
            margin="normal"
            value={editableCounterparty.contract}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                contract: e.target.value,
              })
            }
          />
          <TextField
            label="Ключ"
            fullWidth
            margin="normal"
            value={editableCounterparty.key}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                key: e.target.value,
              })
            }
          />
          <TextField
            label="Примечание"
            fullWidth
            margin="normal"
            multiline
            rows={3}
            value={editableCounterparty.note}
            onChange={(e) =>
              setEditableCounterparty({
                ...editableCounterparty,
                note: e.target.value,
              })
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Отмена</Button>
          <Button variant="contained" onClick={handleSave}>
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default CounterpartiesPage;
