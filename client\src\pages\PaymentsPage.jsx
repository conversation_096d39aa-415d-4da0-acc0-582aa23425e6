// src/pages/PaymentsPage.jsx
import React, { useState, useEffect } from "react";
import api from "../services/api";
import {
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  Box,
  Alert,
} from "@mui/material";

import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ruLocale from "date-fns/locale/ru";

function PaymentsPage() {
  const [payments, setPayments] = useState([]);
  const [counterparties, setCounterparties] = useState([]);

  // Для диалога "Добавить оплату"
  const [openDialog, setOpenDialog] = useState(false);
  const [newPayment, setNewPayment] = useState({
    payment_date: null,
    counterparty_name: "",
    amount: "",
    note: "",
  });

  // Фильтр по дате
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Состояние для импорта Excel
  const [importResult, setImportResult] = useState(null);
  const [showImportResult, setShowImportResult] = useState(false);

  useEffect(() => {
    loadPayments();
    loadCounterparties();
  }, []);

  // useEffect для перезагрузки оплат при изменении фильтров
  useEffect(() => {
    loadPayments();
  }, [startDate, endDate]);

  const loadPayments = async () => {
    try {
      const params = {};
      if (startDate) {
        params.start = startDate
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }
      if (endDate) {
        params.end = endDate
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }

      const res = await api.get("/payments", { params });
      setPayments(res.data);
    } catch (err) {
      console.error("Failed to fetch payments:", err);
    }
  };

  const loadCounterparties = async () => {
    try {
      const res = await api.get("/counterparties");
      const uniqueNames = Array.from(new Set(res.data.map((cp) => cp.name)));
      setCounterparties(uniqueNames);
    } catch (err) {
      console.error("Failed to fetch counterparties:", err);
    }
  };

  // Открыть диалог
  const handleOpenDialog = () => {
    setNewPayment({
      payment_number: "",
      payment_date: null,
      counterparty_name: "",
      amount: "",
      note: "",
    });
    setOpenDialog(true);
  };
  const handleCloseDialog = () => setOpenDialog(false);

  // Сохранение оплаты
  const handleCreatePayment = async () => {
    try {
      // Если не указана дата, возьмём today
      let dateToUse = newPayment.payment_date;

      if (!dateToUse) {
        dateToUse = new Date();
      } else {
        dateToUse = dateToUse
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }

      // Готовим payload
      const payload = {
        payment_number: Number(newPayment.payment_number),
        payment_date: dateToUse,
        counterparty_name: newPayment.counterparty_name,
        amount: Number(newPayment.amount),
        note: newPayment.note,
      };

      await api.post("/payments", payload);
      handleCloseDialog();
      loadPayments();
    } catch (err) {
      console.error("Failed to create payment:", err);
    }
  };

  const handleDeletePayment = async (id) => {
    if (!window.confirm("Удалить оплату?")) return;
    try {
      await api.delete(`/payments/${id}`);
      loadPayments();
    } catch (err) {
      console.error("Failed to delete payment:", err);
    }
  };

  // Функция для импорта Excel
  const handleExcelImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await api.post("/excel/payments-import", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      setImportResult(response.data);
      setShowImportResult(true);
      loadPayments(); // Перезагружаем список оплат

      // Сбрасываем input
      event.target.value = "";
    } catch (error) {
      console.error("Error importing Excel:", error);
      setImportResult({
        message: "Ошибка при импорте файла",
        results: { success: 0, errors: [{ error: error.message }] },
      });
      setShowImportResult(true);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ruLocale}>
      <div>
        <Typography variant="h5" gutterBottom>
          Оплаты
        </Typography>

        {/* Выбор даты */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            mr: 2,
            mb: 2,
            gap: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <DatePicker
              label="С"
              value={startDate}
              onChange={setStartDate}
              slotProps={{ textField: { size: "small", sx: { width: 150 } } }}
            />
            <DatePicker
              label="По"
              value={endDate}
              onChange={setEndDate}
              slotProps={{ textField: { size: "small", sx: { width: 150 } } }}
            />
            <Button variant="contained" onClick={loadPayments}>
              Применить
            </Button>
            <Button
              variant="outlined"
              onClick={() => {
                setStartDate(null);
                setEndDate(null);
                loadPayments();
              }}
            >
              Сбросить
            </Button>
          </Box>
        </Box>

        {/* Кнопки добавления и импорта */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            mr: 2,
            mb: 2,
            gap: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
            <Button variant="contained" onClick={handleOpenDialog}>
              Добавить оплату
            </Button>

            <Button variant="outlined" component="label">
              Загрузить Excel
              <input
                type="file"
                hidden
                accept=".xlsx,.xls"
                onChange={handleExcelImport}
              />
            </Button>
          </Box>
        </Box>
        {/* Таблица оплат */}
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Номер</TableCell>
              <TableCell sx={{ width: 80 }}>Дата</TableCell>
              <TableCell>Контрагент</TableCell>
              <TableCell>Сумма</TableCell>
              <TableCell>Примечание</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell>{payment.payment_number}</TableCell>
                <TableCell>{payment.payment_date}</TableCell>
                <TableCell>{payment.counterparty_name}</TableCell>
                <TableCell>{payment.amount}</TableCell>
                <TableCell>{payment.note}</TableCell>
                <TableCell>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleDeletePayment(payment.id)}
                  >
                    Удалить
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Диалог создания оплаты */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          fullWidth
          maxWidth="sm"
        >
          <DialogTitle>Добавить оплату</DialogTitle>
          <DialogContent>
            <TextField
              label="Номер оплаты"
              type="number"
              fullWidth
              value={newPayment.payment_number}
              onChange={(e) =>
                setNewPayment({ ...newPayment, payment_number: e.target.value })
              }
              sx={{ display: "flex", mt: 2, mb: 2 }}
            />

            <DatePicker
              label="Дата оплаты"
              value={newPayment.payment_date}
              onChange={(date) =>
                setNewPayment({ ...newPayment, payment_date: date })
              }
              sx={{ mb: 2 }}
            />

            <Select
              fullWidth
              displayEmpty
              value={newPayment.counterparty_name}
              onChange={(e) =>
                setNewPayment({
                  ...newPayment,
                  counterparty_name: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            >
              <MenuItem value="">Выберите контрагента</MenuItem>
              {counterparties.map((name) => (
                <MenuItem key={name} value={name}>
                  {name}
                </MenuItem>
              ))}
            </Select>

            <TextField
              label="Сумма"
              type="number"
              fullWidth
              value={newPayment.amount}
              onChange={(e) =>
                setNewPayment({ ...newPayment, amount: e.target.value })
              }
              sx={{ mb: 2 }}
            />

            <TextField
              label="Примечание"
              fullWidth
              multiline
              rows={3}
              value={newPayment.note}
              onChange={(e) =>
                setNewPayment({ ...newPayment, note: e.target.value })
              }
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Отмена</Button>
            <Button variant="contained" onClick={handleCreatePayment}>
              Сохранить
            </Button>
          </DialogActions>
        </Dialog>

        {/* Диалог результатов импорта */}
        <Dialog
          open={showImportResult}
          onClose={() => setShowImportResult(false)}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>Результат импорта Excel</DialogTitle>
          <DialogContent>
            {importResult && (
              <Box>
                <Alert
                  severity={
                    importResult.results?.success > 0 ? "success" : "error"
                  }
                  sx={{ mb: 2 }}
                >
                  {importResult.message}
                </Alert>

                {importResult.results?.success > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h6" color="success.main">
                      Успешно создано: {importResult.results.success} оплат
                    </Typography>
                    {importResult.results.created?.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        {importResult.results.created
                          .slice(0, 5)
                          .map((payment, index) => (
                            <Typography key={index} variant="body2">
                              • Строка {payment.row}:{" "}
                              {payment.counterparty_name} - {payment.amount}{" "}
                              руб.
                            </Typography>
                          ))}
                        {importResult.results.created.length > 5 && (
                          <Typography variant="body2" color="text.secondary">
                            ... и еще {importResult.results.created.length - 5}{" "}
                            оплат
                          </Typography>
                        )}
                      </Box>
                    )}
                  </Box>
                )}

                {importResult.results?.errors?.length > 0 && (
                  <Box>
                    <Typography variant="h6" color="error.main">
                      Ошибки: {importResult.results.errors.length}
                    </Typography>
                    <Box sx={{ mt: 1, maxHeight: 200, overflow: "auto" }}>
                      {importResult.results.errors.map((error, index) => (
                        <Typography
                          key={index}
                          variant="body2"
                          color="error.main"
                        >
                          • Строка {error.row}: {error.error}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowImportResult(false)}>Закрыть</Button>
          </DialogActions>
        </Dialog>
      </div>
    </LocalizationProvider>
  );
}

export default PaymentsPage;
