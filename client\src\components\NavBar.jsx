// src/components/NavBar.jsx
import React, { useContext } from "react";
import { AppBar, Toolbar, Typography, Button, Box } from "@mui/material";
import { AuthContext } from "../context/AuthContext";
import { Link, useNavigate } from "react-router-dom";

function NavBar() {
  const { isLoggedIn, logout } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography
          variant="h6"
          sx={{ flexGrow: 1 }}
          color="inherit"
          component={Link}
          to="/"
        >
          My Trade App
        </Typography>
        {isLoggedIn ? (
          <>
            <Button color="inherit" component={Link} to="/counterparties">
              Контрагенты
            </Button>
            <Button color="inherit" component={Link} to="/products">
              Товары
            </Button>
            <Button color="inherit" component={Link} to="/orders">
              Заказы
            </Button>
            <Button color="inherit" component={Link} to="/payments">
              Оплаты
            </Button>
            <Button color="inherit" component={Link} to="/sales-report">
              Отчёт
            </Button>
            {/* Кнопка Выйти */}
            <Button color="inherit" onClick={handleLogout}>
              Выйти
            </Button>
          </>
        ) : (
          <Button color="inherit" component={Link} to="/login">
            Войти
          </Button>
        )}
      </Toolbar>
    </AppBar>
  );
}

export default NavBar;
