// server/routes/payment.routes.js
const { Router } = require('express');
const router = Router();

const {
  createPayment,
  getAllPayments,
  getPaymentById,
  updatePayment,
  deletePayment
} = require('../controllers/payment.controller');

// POST /api/payments
router.post('/', createPayment);

// GET /api/payments
router.get('/', getAllPayments);

// GET /api/payments/:id
router.get('/:id', getPaymentById);

// PUT /api/payments/:id
router.put('/:id', updatePayment);

// DELETE /api/payments/:id
router.delete('/:id', deletePayment);

module.exports = router;
