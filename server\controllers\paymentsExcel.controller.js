// server/controllers/paymentsExcel.controller.js
const XLSX = require('xlsx');
const Payment = require('../models/payments.model');
const Counterparty = require('../models/counterparties.model');

module.exports = {
  // POST /api/excel/payments-import
  importPaymentsFromExcel: async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // 1) Читаем Excel файл
      const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0]; // берём первый лист
      const worksheet = workbook.Sheets[sheetName];

      // 2) Конвертируем в JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length <= 1) {
        return res.status(400).json({ error: 'Excel file is empty or contains only headers' });
      }

      // 3) Получаем всех контрагентов для сопоставления по ИНН
      const counterparties = await Counterparty.findAll();
      const innToCounterpartyMap = {};

      counterparties.forEach(cp => {
        if (cp.inn) {
          innToCounterpartyMap[cp.inn.toString()] = cp.name;
        }
      });

      // 4) Обрабатываем строки (пропускаем заголовок)
      const results = {
        success: 0,
        errors: [],
        created: []
      };

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];

        try {
          // Извлекаем данные из столбцов
          const dateValue = row[0]; // Первый столбец - дата
          const paymentNumber = row[1]; // Второй столбец - номер
          const counterpartyInn = row[4]; // Пятый столбец - ИНН контрагента
          const amount = row[8]; // Девятый столбец - сумма
          const noteValue = row[9]; // Десятый столбец - примечание

          // Проверяем обязательные поля
          if (!dateValue || !paymentNumber || !counterpartyInn || !amount) {
            results.errors.push({
              row: i + 1,
              error: 'Missing required fields (date, number, inn, or amount)'
            });
            continue;
          }

          // Обрабатываем дату
          let paymentDate;
          if (typeof dateValue === 'number') {
            // Excel дата как число
            const excelDate = new Date((dateValue - 25569) * 86400 * 1000);
            paymentDate = excelDate.toISOString().split('T')[0];
          } else if (typeof dateValue === 'string') {
            // Ожидаем формат «DD.MM.YYYY»
            const parts = dateValue.split('.');
            if (parts.length !== 3) {
              results.errors.push({
                row: i + 1,
                error: 'Invalid date format, expected DD.MM.YYYY'
              });
              continue;
            }
            let [day, month, year] = parts;
            // на всякий случай дополняем «0» слева
            day = day.padStart(2, '0');
            month = month.padStart(2, '0');
            // собираем в ISO‑дату
            paymentDate = `${year}-${month}-${day}`;  // «2025-01-22»

            // (если нужно валидировать дальше, можно new Date(paymentDate) и проверять .getTime())
          } else {
            results.errors.push({
              row: i + 1,
              error: 'Invalid date format'
            });
            continue;
          }

          // Ищем контрагента по ИНН
          const counterpartyName = innToCounterpartyMap[counterpartyInn.toString()];
          if (!counterpartyName) {
            results.errors.push({
              row: i + 1,
              error: `Counterparty with INN ${counterpartyInn} not found`
            });
            continue;
          }

          // Проверяем сумму
          const parsedAmount = parseFloat(amount);
          if (isNaN(parsedAmount) || parsedAmount <= 0) {
            results.errors.push({
              row: i + 1,
              error: 'Invalid amount'
            });
            continue;
          }

          // Проверяем, не существует ли уже оплата с таким номером
          const existingPayment = await Payment.findOne({
            where: { payment_number: paymentNumber.toString() }
          });

          if (existingPayment) {
            results.errors.push({
              row: i + 1,
              error: `Payment with number ${paymentNumber} already exists`
            });
            continue;
          }

          // Создаём оплату
          const newPayment = await Payment.create({
            payment_number: paymentNumber.toString(),
            payment_date: paymentDate,
            counterparty_name: counterpartyName,
            amount: parsedAmount,
            note: noteValue
          });

          results.success++;
          results.created.push({
            row: i + 1,
            payment_number: paymentNumber,
            counterparty_name: counterpartyName,
            amount: parsedAmount,
            payment_date: paymentDate,
            note: noteValue
          });

        } catch (error) {
          console.error(`Error processing row ${i + 1}:`, error);
          results.errors.push({
            row: i + 1,
            error: error.message
          });
        }
      }
      console.log('results:', results);
      return res.json({
        message: `Import completed. ${results.success} payments created, ${results.errors.length} errors`,
        results
      });

    } catch (error) {
      console.error('Error importing payments from Excel:', error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  }
};
