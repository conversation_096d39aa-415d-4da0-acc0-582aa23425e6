// server/index.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const sequelize = require('./config/db'); // импорт sequelize

const { IpInfo, Counterparty, Product, Order, OrderItem, Payment } = require('./models/associations');

// Импорт роута контрагентов
const counterpartyRoutes = require('./routes/counterparty.routes');
const productRoutes = require('./routes/product.routes');
const orderRoutes = require('./routes/order.routes');
const excelRoutes = require('./routes/excel.routes');
const htmlPreviewRoutes = require('./routes/htmlPreview.routes');
const pdfPuppeteerRoutes = require('./routes/pdfPuppeteer.routes');
const authRoutes = require('./routes/auth.routes');
const authMiddleware = require('./middleware/authMiddleware');
// app.use('/api/orders', authMiddleware, orderRoutes);
// app.use('/api/products', authMiddleware, productRoutes);
const reportRoutes = require('./routes/report.routes');
const paymentRoutes = require('./routes/payment.routes');

const app = express();
const PORT = process.env.PORT || 5000;

app.use(cors());
app.use(express.json());
// Раздача статики
app.use(express.static(path.join(__dirname, 'public')));

// Подключаем роутер
app.use('/api/counterparties', counterpartyRoutes);
app.use('/api/products', productRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/excel', excelRoutes);
app.use('/api/html', htmlPreviewRoutes);
app.use('/api/pdf', pdfPuppeteerRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/payments', paymentRoutes);

// Тестовый эндпойнт
app.get('/', (req, res) => {
  res.send('Hello from My Trade App server!');
});

app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Функция для запуска сервера и подключения к БД
const startServer = async () => {
  try {
    // Проверяем подключение
    await sequelize.authenticate();
    console.log('Connection to PostgreSQL has been established successfully.');

    // Синхронизируем модели: создаём таблицы, если их нет
    // Внимание: force: true перезапишет таблицы! Для боевого проекта лучше использовать миграции
    await sequelize.sync({ alter: true });
    console.log('All models were synchronized successfully.');

    // Запускаем сервер
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
};

// Пример - POST /test-counterparty

app.post('/test-counterparty', async (req, res) => {
  try {
    const newC = await Counterparty.create({
      name: 'Test Store',
      inn: '1234567890',
      ogrn: '0987654321',
      address: 'Moscow, Red Square',
      contact_person: 'Ivan Ivanov',
      phone: '****** 111-22-33',
      email: '<EMAIL>'
    });
    return res.json(newC);
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Something went wrong' });
  }
});

startServer();
