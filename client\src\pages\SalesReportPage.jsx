// src/pages/SalesReportPage.jsx
import React, { useState, useEffect } from "react";
import api from "../services/api";
import {
  Typography,
  Button,
  Table,
  TableHead,
  TableCell,
  TableBody,
  TableRow,
  Box,
  Select,
  MenuItem,
  Divider,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ruLocale from "date-fns/locale/ru";

function SalesReportPage() {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [report, setReport] = useState(null);

  // Состояние для акта сверки
  const [aktStartDate, setAktStartDate] = useState(null);
  const [aktEndDate, setAktEndDate] = useState(null);
  const [selectedCounterparty, setSelectedCounterparty] = useState("");
  const [counterparties, setCounterparties] = useState([]);

  useEffect(() => {
    loadCounterparties();
  }, []);

  const loadCounterparties = async () => {
    try {
      const res = await api.get("/counterparties");
      setCounterparties(res.data);
    } catch (err) {
      console.error("Failed to fetch counterparties:", err);
    }
  };

  // Получаем уникальные названия контрагентов
  const getUniqueCounterpartyNames = () => {
    const names = new Set();
    counterparties.forEach((cp) => {
      if (cp.name) {
        names.add(cp.name);
      }
    });
    return Array.from(names).sort();
  };

  // Функции для акта сверки
  const formatDateForAPI = (date) => {
    if (!date) return "";
    return date
      .toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      })
      .split(".")
      .reverse()
      .join("-");
  };

  const handleViewAktSverki = () => {
    if (!aktStartDate || !aktEndDate || !selectedCounterparty) {
      alert("Выберите период и контрагента");
      return;
    }

    const startDateStr = formatDateForAPI(aktStartDate);
    const endDateStr = formatDateForAPI(aktEndDate);

    const url = `/api/html/akt-sverki?counterparty_name=${encodeURIComponent(
      selectedCounterparty
    )}&start_date=${startDateStr}&end_date=${endDateStr}`;
    window.open(url, "_blank");
  };

  const handleDownloadAktSverki = () => {
    if (!aktStartDate || !aktEndDate || !selectedCounterparty) {
      alert("Выберите период и контрагента");
      return;
    }

    const startDateStr = formatDateForAPI(aktStartDate);
    const endDateStr = formatDateForAPI(aktEndDate);

    const url = `/api/pdf/akt-sverki?counterparty_name=${encodeURIComponent(
      selectedCounterparty
    )}&start_date=${startDateStr}&end_date=${endDateStr}`;
    window.open(url, "_blank");
  };

  const handleGenerate = async () => {
    if (!startDate || !endDate) {
      alert("Выберите обе даты");
      return;
    }
    try {
      // Форматируем даты в YYYY-MM-DD
      const s = startDate
        .toLocaleDateString("ru-RU", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        })
        .split(".")
        .reverse()
        .join("-"); // Преобразование в формат YYYY-MM-DD
      const e = endDate
        .toLocaleDateString("ru-RU", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        })
        .split(".")
        .reverse()
        .join("-"); // Преобразование в формат YYYY-MM-DD

      const res = await api.get("/reports/sales", {
        params: { start: s, end: e },
      });
      setReport(res.data);
    } catch (err) {
      console.error("Failed to fetch report:", err);
    }
  };

  // Подсчёт "итог" по столбцам
  let totalCount = 0;
  if (report && report.products) {
    report.products.forEach((p) => {
      totalCount += p.totalQuantity;
    });
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ruLocale}>
      <div>
        <Typography variant="h5" gutterBottom>
          Отчёт по продажам
        </Typography>
        <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
          <DatePicker
            label="Дата начала"
            value={startDate}
            onChange={(newValue) => setStartDate(newValue)}
            //renderInput={(params) => <div {...params} />}
          />
          <DatePicker
            label="Дата окончания"
            value={endDate}
            onChange={(newValue) => setEndDate(newValue)}
            //renderInput={(params) => <div {...params} />}
          />
          <Button variant="contained" onClick={handleGenerate}>
            Сформировать
          </Button>
        </Box>

        {report && (
          <div>
            <Typography>Заказов в периоде: {report.ordersCount}</Typography>
            <Typography>Общая сумма: {report.totalSum} руб.</Typography>

            {/* Таблица по продуктам */}
            <Table sx={{ mt: 2 }}>
              <TableHead>
                <TableRow>
                  <TableCell>Продукт</TableCell>
                  <TableCell>Сумма продажи</TableCell>
                  <TableCell>Количество штук</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {report.products.map((p, idx) => (
                  <TableRow key={idx}>
                    <TableCell>{p.name}</TableCell>
                    <TableCell>{p.totalPrice}</TableCell>
                    <TableCell>{p.totalQuantity}</TableCell>
                  </TableRow>
                ))}
                {/* Итог */}
                <TableRow>
                  <TableCell>
                    <b>Итого</b>
                  </TableCell>
                  <TableCell>
                    <b>{report.totalSum}</b>
                  </TableCell>
                  <TableCell>
                    <b>{totalCount}</b>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        )}

        {/* Разделитель */}
        <Divider sx={{ my: 4 }} />

        {/* Секция акта сверки */}
        <Typography variant="h5" gutterBottom>
          Акт сверки взаимных расчётов
        </Typography>

        <Box sx={{ display: "flex", gap: 2, mb: 2, flexWrap: "wrap" }}>
          <DatePicker
            label="Дата начала"
            value={aktStartDate}
            onChange={(newValue) => setAktStartDate(newValue)}
            slotProps={{ textField: { size: "small", sx: { width: 150 } } }}
          />
          <DatePicker
            label="Дата окончания"
            value={aktEndDate}
            onChange={(newValue) => setAktEndDate(newValue)}
            slotProps={{ textField: { size: "small", sx: { width: 150 } } }}
          />
          <Select
            value={selectedCounterparty}
            onChange={(e) => setSelectedCounterparty(e.target.value)}
            displayEmpty
            size="small"
            sx={{ minWidth: 200 }}
          >
            <MenuItem value="">Выберите контрагента</MenuItem>
            {getUniqueCounterpartyNames().map((name) => (
              <MenuItem key={name} value={name}>
                {name}
              </MenuItem>
            ))}
          </Select>
          <Button variant="contained" onClick={handleViewAktSverki}>
            Просмотреть
          </Button>
          <Button variant="outlined" onClick={handleDownloadAktSverki}>
            Скачать
          </Button>
        </Box>
      </div>
    </LocalizationProvider>
  );
}

export default SalesReportPage;
