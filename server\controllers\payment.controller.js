// server/controllers/payment.controller.js

const Payment = require('../models/payments.model');
const { Op } = require('sequelize');

module.exports = {
  // 1) CREATE PAYMENT (POST /api/payments)
  createPayment: async (req, res) => {
    try {
      const { payment_number, payment_date, counterparty_name, amount, note } = req.body;

      // Проверим, что обязательные поля указаны
      if (!counterparty_name || !amount) {
        return res.status(400).json({ error: 'counterparty_name and amount are required' });
      }

      // Получаем последний payment_number
      const lastPayment = await Payment.findOne({
        order: [['id', 'DESC']], // Сортируем по номеру в убывающем порядке
      });

      // Устанавливаем новый payment_number
      if (!payment_number) {
        new_number = lastPayment ? parseInt(lastPayment.payment_number, 10) + 1 : 1;
      }

      // Создаём оплату
      const newPayment = await Payment.create({
        payment_number: payment_number || new_number.toString(),
        payment_date: payment_date || new Date(), // если не передана дата, берём текущую
        counterparty_name,
        amount,
        note: note || ''
      });

      return res.status(201).json(newPayment);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 2) GET ALL PAYMENTS (GET /api/payments)
  getAllPayments: async (req, res) => {
    try {
      const { start, end, counterparty_name } = req.query;
      console.log(start, end, counterparty_name);
      const where = {};

      if (start) {
        where.payment_date = { [Op.gte]: start };
      }
      if (end) {
        where.payment_date = {
          ...(where.payment_date || {}),
          [Op.lte]: end
        };
      }

      // Фильтрация по названию контрагента
      if (counterparty_name && counterparty_name !== 'all') {
        where.counterparty_name = {
          [Op.iLike]: `%${counterparty_name}%` // поиск по частичному совпадению (регистронезависимый)
        };
      }

      const payments = await Payment.findAll({
        where,
        order: [['payment_date', 'DESC'], ['id', 'DESC']] // сортировка по убыванию даты
      });
      return res.json(payments);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 3) GET ONE PAYMENT (GET /api/payments/:id)
  getPaymentById: async (req, res) => {
    try {
      const { id } = req.params;
      const payment = await Payment.findByPk(id);

      if (!payment) {
        return res.status(404).json({ error: 'Payment not found' });
      }

      return res.json(payment);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 4) UPDATE PAYMENT (PUT /api/payments/:id)
  updatePayment: async (req, res) => {
    try {
      const { id } = req.params;
      const { payment_date, counterparty_name, amount, note } = req.body;

      const payment = await Payment.findByPk(id);
      if (!payment) {
        return res.status(404).json({ error: 'Payment not found' });
      }

      // Обновляем поля
      if (payment_date) payment.payment_date = payment_date;
      if (counterparty_name) payment.counterparty_name = counterparty_name;
      if (amount) payment.amount = amount;
      if (note !== undefined) payment.note = note;

      await payment.save();

      return res.json(payment);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 5) DELETE PAYMENT (DELETE /api/payments/:id)
  deletePayment: async (req, res) => {
    try {
      const { id } = req.params;
      const payment = await Payment.findByPk(id);

      if (!payment) {
        return res.status(404).json({ error: 'Payment not found' });
      }

      await payment.destroy();
      return res.json({ message: 'Payment deleted successfully' });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  }
};
