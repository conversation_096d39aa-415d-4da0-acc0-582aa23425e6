// server/routes/htmlPreview.routes.js
const { Router } = require('express');
const { viewUPDHTML } = require('../controllers/htmlPreview.controller');
const { viewAktSverkiHTML } = require('../controllers/aktSverkiPreview.controller');
const router = Router();

// GET /api/html/order/:orderId — вернёт уже скомпилированную HTML-страницу
router.get('/order/:orderId', viewUPDHTML);

// GET /api/html/akt-sverki — вернёт HTML акта сверки
router.get('/akt-sverki', viewAktSverkiHTML);

module.exports = router;
