// server/controllers/counterparty.controller.js

const { Counterparty } = require('../models/associations');

// В контроллере будут функции (методы), которые вызываются из роутов
// Далее – пример CRUD

module.exports = {
  // CREATE (POST /api/counterparties)
  createCounterparty: async (req, res) => {
    try {
      const { name, inn, kpp, address, legal_address, contract, key, note } = req.body;

      // Валидация: например, проверим, что name и inn заполнены
      if (!name || !inn) {
        return res.status(400).json({ error: 'Name and INN are required' });
      }

      // Создаём новую запись
      const newCounterparty = await Counterparty.create({
        name,
        inn,
        kpp,
        address,
        legal_address,
        contract,
        key,
        note
      });

      return res.status(201).json(newCounterparty);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // READ ALL (GET /api/counterparties)
  getAllCounterparties: async (req, res) => {
    try {
      const counterparties = await Counterparty.findAll({ order: [['id', 'ASC']] });
      return res.json(counterparties);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // READ ONE (GET /api/counterparties/:id)
  getCounterpartyById: async (req, res) => {
    try {
      const { key } = req.params;
      const counterparty = await Counterparty.findOne({ where: { key: key } });

      if (!counterparty) {
        return res.status(404).json({ error: 'Counterparty not found' });
      }

      return res.json(counterparty);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // UPDATE (PUT /api/counterparties/:id)
  updateCounterparty: async (req, res) => {
    try {
      const { id } = req.params;
      const { name, inn, kpp, address, legal_address, contract, key, note } = req.body;

      // Ищем запись
      const counterparty = await Counterparty.findByPk(id);
      if (!counterparty) {
        return res.status(404).json({ error: 'Counterparty not found' });
      }

      // Обновляем данные
      counterparty.name = name || counterparty.name;
      counterparty.inn = inn || counterparty.inn;
      counterparty.kpp = kpp || counterparty.kpp;
      counterparty.address = address || counterparty.address;
      counterparty.legal_address = legal_address || counterparty.legal_address;
      counterparty.contract = contract || counterparty.contract;
      counterparty.key = key || counterparty.key;
      if (note !== undefined) counterparty.note = note;

      await counterparty.save();

      return res.json(counterparty);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // DELETE (DELETE /api/counterparties/:id)
  deleteCounterparty: async (req, res) => {
    try {
      const { id } = req.params;
      const counterparty = await Counterparty.findByPk(id);

      if (!counterparty) {
        return res.status(404).json({ error: 'Counterparty not found' });
      }

      await counterparty.destroy();
      return res.json({ message: 'Counterparty deleted successfully' });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },
};
