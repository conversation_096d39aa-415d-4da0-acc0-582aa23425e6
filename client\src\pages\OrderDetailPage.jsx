// src/pages/OrderDetailPage.jsx
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import api from "../services/api";
import {
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  TextField,
  IconButton,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";

function OrderDetailPage() {
  const { id } = useParams();
  const [order, setOrder] = useState(null);

  // Данные для редактирования
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [editOrderData, setEditOrderData] = useState({
    order_date: "",
    counterparty_id: "",
    items: [],
  });
  const [counterparties, setCounterparties] = useState([]);
  const [products, setProducts] = useState([]);

  useEffect(() => {
    loadOrder();
    loadCounterparties();
    loadProducts();
  }, [id]);

  const loadOrder = async () => {
    const res = await api.get(`/orders/${id}`);
    setOrder(res.data);
  };
  const loadCounterparties = async () => {
    const res = await api.get("/counterparties");
    setCounterparties(res.data);
  };
  const loadProducts = async () => {
    const res = await api.get("/products");
    setProducts(res.data);
  };

  if (!order) {
    return <Typography>Загрузка...</Typography>;
  }

  // Кнопка "Редактировать"
  const handleOpenEdit = () => {
    setEditOrderData({
      order_date: order.order_date,
      counterparty_id: order.counterparty_id || "",
      items: order.order_items.map((i) => ({
        id: i.id,
        product_id: i.product_id,
        quantity: i.quantity,
      })),
    });
    setOpenEditDialog(true);
  };

  const handleCloseEdit = () => {
    setOpenEditDialog(false);
  };

  // Логика редактирования товаров
  const handleAddItem = () => {
    setEditOrderData((prev) => ({
      ...prev,
      items: [...prev.items, { product_id: "", quantity: 1 }],
    }));
  };
  const handleRemoveItem = (idx) => {
    setEditOrderData((prev) => {
      const newItems = [...prev.items];
      newItems.splice(idx, 1);
      return { ...prev, items: newItems };
    });
  };
  const handleChangeItem = (idx, field, value) => {
    setEditOrderData((prev) => {
      const newItems = [...prev.items];
      newItems[idx][field] = value;
      return { ...prev, items: newItems };
    });
  };

  // Сохранение изменений
  const handleSaveOrder = async () => {
    try {
      // Готовим payload
      const payload = {
        order_date: editOrderData.order_date,
        counterparty_id: Number(editOrderData.counterparty_id),
        items: editOrderData.items.map((i) => ({
          product_id: Number(i.product_id),
          quantity: Number(i.quantity),
        })),
      };

      // Добавляем номер заказа, если указан
      if (editOrderData.order_number && editOrderData.order_number.trim()) {
        payload.order_number = editOrderData.order_number.trim();
      }
      await api.put(`/orders/${id}`, payload);
      setOpenEditDialog(false);
      loadOrder(); // заново загружаем заказ
    } catch (err) {
      console.error("Failed to update order:", err);
    }
  };

  // Кнопки для просмотра/скачивания
  const handleViewUPD = () => {
    window.open(`/api/html/order/${order.id}`, "_blank");
  };
  const handleDownloadPDF = () => {
    window.open(`/api/pdf/puppeteer/${order.id}`, "_blank");
  };

  return (
    <div>
      <Typography variant="h5" gutterBottom>
        Заказ #{order.order_number}
      </Typography>
      <Typography>Дата: {order.order_date}</Typography>
      <Typography>Контрагент: {order.counterparty?.name}</Typography>

      <Table sx={{ mt: 2 }}>
        <TableHead>
          <TableRow>
            <TableCell>Товар</TableCell>
            <TableCell>Кол-во</TableCell>
            <TableCell>Цена</TableCell>
            <TableCell>Сумма</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {order.order_items.map((item) => (
            <TableRow key={item.id}>
              <TableCell>{item.product?.name}</TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>{item.price}</TableCell>
              <TableCell>{item.sum}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Typography sx={{ mt: 2 }}>Итого: {order.total_sum}</Typography>

      <Button variant="outlined" sx={{ mt: 2, mr: 1 }} onClick={handleOpenEdit}>
        Редактировать
      </Button>
      <Button variant="outlined" sx={{ mt: 2, mr: 1 }} onClick={handleViewUPD}>
        Посмотреть УПД
      </Button>
      <Button variant="contained" sx={{ mt: 2 }} onClick={handleDownloadPDF}>
        Скачать PDF
      </Button>

      {/* Диалог редактирования */}
      <Dialog
        open={openEditDialog}
        onClose={handleCloseEdit}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Редактировать заказ</DialogTitle>
        <DialogContent>
          <Select
            fullWidth
            sx={{ mt: 2 }}
            displayEmpty
            value={editOrderData.counterparty_id}
            onChange={(e) =>
              setEditOrderData({
                ...editOrderData,
                counterparty_id: e.target.value,
              })
            }
          >
            <MenuItem value="">Выберите контрагента</MenuItem>
            {counterparties.map((ct) => (
              <MenuItem key={ct.id} value={ct.id}>
                {ct.key} {ct.name} {ct.address}
              </MenuItem>
            ))}
          </Select>

          <TextField
            label="Дата заказа (YYYY-MM-DD)"
            fullWidth
            margin="normal"
            value={editOrderData.order_date}
            onChange={(e) =>
              setEditOrderData({ ...editOrderData, order_date: e.target.value })
            }
          />

          <TextField
            label="Номер заказа"
            fullWidth
            margin="normal"
            value={editOrderData.order_number || ""}
            onChange={(e) =>
              setEditOrderData({
                ...editOrderData,
                order_number: e.target.value,
              })
            }
          />

          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleAddItem}
            sx={{ mt: 1 }}
          >
            Добавить позицию
          </Button>

          {editOrderData.items.map((item, idx) => (
            <div
              key={idx}
              style={{ display: "flex", alignItems: "center", marginTop: 10 }}
            >
              <Select
                sx={{ mr: 1, minWidth: 400 }}
                displayEmpty
                value={item.product_id}
                onChange={(e) =>
                  handleChangeItem(idx, "product_id", e.target.value)
                }
              >
                <MenuItem value="">Товар</MenuItem>
                {products.map((p) => (
                  <MenuItem key={p.id} value={p.id}>
                    {p.name}
                  </MenuItem>
                ))}
              </Select>

              <TextField
                label="Количество"
                type="number"
                sx={{ width: 100, mr: 1 }}
                value={item.quantity}
                onChange={(e) =>
                  handleChangeItem(idx, "quantity", e.target.value)
                }
              />

              <IconButton color="error" onClick={() => handleRemoveItem(idx)}>
                <DeleteIcon />
              </IconButton>
            </div>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEdit}>Отмена</Button>
          <Button variant="contained" onClick={handleSaveOrder}>
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default OrderDetailPage;
