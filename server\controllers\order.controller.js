// server/controllers/order.controller.js

const { Order, OrderItem, Product, Counterparty } = require('../models/associations');
const { Op } = require('sequelize'); // иногда полезно для операций вроде where: { price: { [Op.gt]: 100 } }

module.exports = {
  // 1) CREATE ORDER (POST /api/orders)
  // Пример тела запроса:
  // {
  //   "counterparty_id": 1,
  //   "order_date": "2025-01-01",
  //   "items": [
  //     { "product_id": 1, "quantity": 2 },
  //     { "product_id": 3, "quantity": 5 }
  //   ]
  // }
  createOrder: async (req, res) => {
    const transaction = await Order.sequelize.transaction(); // Начинаем транзакцию
    try {
      const { counterparty_id, order_date, items } = req.body;

      // Проверим, что контрагент указан
      if (!counterparty_id || !items || !Array.isArray(items) || items.length === 0) {
        return res.status(400).json({ error: 'counterparty_id and items are required' });
      }

      // Получаем последний order_number
      const lastOrder = await Order.findOne({
        order: [['id', 'DESC']], // Сортируем по номеру в убывающем порядке
        transaction,
      });

      // Устанавливаем новый order_number
      const order_number = lastOrder ? parseInt(lastOrder.order_number, 10) + 1 : 1;

      // Создаём сам заказ
      const newOrder = await Order.create({
        order_number,
        order_date: order_date || new Date(), // если не передана дата, берём текущую
        total_sum: 0, // посчитаем позже
        counterparty_id
      },
        { transaction } // Включаем транзакцию
      );

      let totalSum = 0;

      // Создаём позиции заказа
      for (const item of items) {
        const { product_id, quantity } = item;
        // Находим товар, чтобы взять его текущую цену
        const product = await Product.findByPk(product_id, { transaction });
        if (!product) {
          // Если товар не найден, можно либо пропустить, либо вернуть ошибку
          // Для примера вернём ошибку
          await transaction.rollback(); // Откатываем транзакцию
          await newOrder.destroy(); // удалим заказ, так как он невалиден
          return res.status(400).json({ error: `Product with id ${product_id} not found` });
        }

        const price = product.price; // цена на момент заказа
        const sum = price * quantity;

        // Создаём позицию
        await OrderItem.create({
          order_id: newOrder.id,
          product_id: product.id,
          quantity,
          price,
          sum
        },
          { transaction } // Указываем транзакцию
        );

        // Добавляем к общей сумме
        totalSum += sum;
      }

      // Обновляем total_sum у заказа
      newOrder.total_sum = totalSum;
      await newOrder.save({ transaction });

      // Подтверждаем транзакцию
      await transaction.commit();

      // Возвращаем готовый заказ, можно и с позициями (include)
      const orderWithItems = await Order.findByPk(newOrder.id, {
        include: [
          { model: OrderItem, include: [Product] },
          { model: Counterparty }
        ]
      });
      return res.status(201).json(orderWithItems);

    } catch (error) {
      await transaction.rollback(); // Откатываем транзакцию в случае ошибки
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 2) GET ALL ORDERS (GET /api/orders)
  getAllOrders: async (req, res) => {
    try {
      const { start, end, edo_sent, counterparty_name, counterparty_key } = req.query;
      console.log(start, end, edo_sent, counterparty_name, counterparty_key);
      const where = {};
      const include = [
        { model: Counterparty },
        { model: OrderItem, include: [Product] }
      ];

      if (start) {
        where.order_date = { [Op.gte]: start };
      }
      if (end) {
        where.order_date = {
          ...(where.order_date || {}),
          [Op.lte]: end
        };
      }
      // Фильтрация по edo_sent
      if (edo_sent !== undefined) {
        if (edo_sent === 'true') {
          where.edo_sent = true;
        } else if (edo_sent === 'false') {
          where.edo_sent = false;
        }
        // Если edo_sent === 'all' или любое другое значение, не добавляем фильтр
      }

      // Фильтрация по названию контрагента или ключу
      if ((counterparty_name && counterparty_name !== 'all') || (counterparty_key && counterparty_key !== 'all')) {
        const counterpartyWhere = {};

        if (counterparty_key && counterparty_key !== 'all') {
          // Фильтрация по конкретному ключу имеет приоритет
          counterpartyWhere.key = counterparty_key;
        } else if (counterparty_name && counterparty_name !== 'all') {
          // Фильтрация по названию контрагента
          counterpartyWhere.name = {
            [Op.iLike]: `%${counterparty_name}%` // поиск по частичному совпадению (регистронезависимый)
          };
        }

        include[0] = {
          model: Counterparty,
          where: counterpartyWhere
        };
      }

      // Включим связанные модели, чтобы показать контрагента и позиции
      const orders = await Order.findAll({
        where,
        include,
        order: [['order_date', 'DESC'], ['id', 'DESC']] // сортировка по убыванию даты
      });
      return res.json(orders);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 3) GET ONE ORDER (GET /api/orders/:id)
  getOrderById: async (req, res) => {
    try {
      const { id } = req.params;
      const order = await Order.findByPk(id, {
        include: [
          { model: Counterparty },
          { model: OrderItem, include: [Product] }
        ]
      });

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      return res.json(order);
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 4) UPDATE ORDER (PUT /api/orders/:id)
  // Логика бывает разной: 
  // - Можно дать возможность менять только статус или дату
  // - Или позволить пересоздавать позиции и пересчитывать сумму
  updateOrder: async (req, res) => {
    try {
      const { id } = req.params;
      const { order_date, counterparty_id, items, edo_sent } = req.body;

      const order = await Order.findByPk(id, {
        include: [OrderItem]
      });
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Обновим дату (если пришла)
      if (order_date) {
        order.order_date = order_date;
      }

      // Обновим контрагента (если пришел)
      if (counterparty_id) {
        order.counterparty_id = counterparty_id;
      }

      // если передали edo_sent — обновляем
      if (typeof edo_sent === 'boolean') {
        order.edo_sent = edo_sent;
      }

      // Если items есть, пересоздадим (или обновим) позиции
      // Для упрощения удалим все старые и создадим заново
      // В боевых системах часто делают более сложную логику
      let totalSum = 0;
      if (Array.isArray(items) && items.length > 0) {
        // Удаляем старые позиции
        await OrderItem.destroy({ where: { order_id: order.id } });

        // Создаём новые
        for (const item of items) {
          const product = await Product.findByPk(item.product_id);
          if (!product) {
            return res.status(400).json({ error: `Product with id ${item.product_id} not found` });
          }
          const price = product.price;
          const sum = price * item.quantity;

          await OrderItem.create({
            order_id: order.id,
            product_id: product.id,
            quantity: item.quantity,
            price,
            sum
          });

          totalSum += sum;
        }
      } else {
        // Если items не переданы, оставим старые позиции (или удалим все - по желанию)
        // Для примера, если не пришли новые items, то не трогаем старые
        // но тогда нужно заново рассчитать totalSum
        const existingItems = await OrderItem.findAll({ where: { order_id: order.id } });
        totalSum = existingItems.reduce((acc, it) => acc + parseFloat(it.sum), 0);
      }

      order.total_sum = totalSum;
      await order.save();

      const updatedOrder = await Order.findByPk(order.id, {
        include: [
          { model: Counterparty },
          { model: OrderItem, include: [Product] }
        ]
      });

      return res.json(updatedOrder);

    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },

  // 5) DELETE ORDER (DELETE /api/orders/:id)
  deleteOrder: async (req, res) => {
    try {
      const { id } = req.params;
      const order = await Order.findByPk(id);
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      await order.destroy();
      return res.json({ message: 'Order deleted successfully' });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: 'Internal Server Error' });
    }
  },
};
