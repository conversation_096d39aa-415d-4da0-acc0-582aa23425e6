import { useState } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.css";

import React, { useContext } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import HomePage from "./pages/HomePage";
import CounterpartiesPage from "./pages/CounterpartiesPage";
import ProductsPage from "./pages/ProductsPage";
import OrdersPage from "./pages/OrdersPage";
import OrderCreatePage from "./pages/OrderCreatePage";
import OrderDetailPage from "./pages/OrderDetailPage";
import ExcelImportPage from "./pages/ExcelImportPage";
import SalesReportPage from "./pages/SalesReportPage";
import PaymentsPage from "./pages/PaymentsPage";
import Layout from "./components/Layout";
import LoginPage from "./pages/LoginPage";
import { AuthContext } from "./context/AuthContext";

// Пример защищённого маршрута
function PrivateRoute({ children }) {
  const { isLoggedIn, loading } = useContext(AuthContext);

  if (loading) {
    // Пока грузим инфу о токене — покажем заглушку
    return <div>Загрузка авторизации...</div>;
  }

  if (!isLoggedIn) {
    // Неавторизован — уходим на /login
    return <Navigate to="/login" />;
  }

  // Иначе отрисовываем защищённый компонент
  return children;
}

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route
            path="/"
            element={
              <PrivateRoute>
                <HomePage />
              </PrivateRoute>
            }
          />
          <Route path="/login" element={<LoginPage />} />

          <Route
            path="/counterparties"
            element={
              <PrivateRoute>
                <CounterpartiesPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/products"
            element={
              <PrivateRoute>
                <ProductsPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/orders"
            element={
              <PrivateRoute>
                <OrdersPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/orders/create"
            element={
              <PrivateRoute>
                <OrderCreatePage />
              </PrivateRoute>
            }
          />
          <Route
            path="/orders/:id"
            element={
              <PrivateRoute>
                <OrderDetailPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/payments"
            element={
              <PrivateRoute>
                <PaymentsPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/excel-import"
            element={
              <PrivateRoute>
                <ExcelImportPage />
              </PrivateRoute>
            }
          />
          <Route
            path="/sales-report"
            element={
              <PrivateRoute>
                <SalesReportPage />
              </PrivateRoute>
            }
          />

          {/* ...прочие роуты */}
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
