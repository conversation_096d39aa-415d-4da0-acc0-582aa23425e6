// server/routes/pdfPuppeteer.routes.js
const { Router } = require('express');
const { generateUPDPDF } = require('../controllers/pdfPuppeteer.controller');
const { generateAktSverkiPDF } = require('../controllers/aktSverkiPdf.controller');

const router = Router();

// GET /api/pdf/puppeteer/:orderId
router.get('/puppeteer/:orderId', generateUPDPDF);

// GET /api/pdf/akt-sverki
router.get('/akt-sverki', generateAktSverkiPDF);

module.exports = router;
