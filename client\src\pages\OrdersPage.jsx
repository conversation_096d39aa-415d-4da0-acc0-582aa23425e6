// src/pages/OrdersPage.jsx
import React, { useState, useEffect, useCallback } from "react";
import api from "../services/api";
import { Link } from "react-router-dom";
import {
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  IconButton,
  Box,
  Menu,
  ListItemText,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import SettingsIcon from "@mui/icons-material/Settings";

import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ruLocale from "date-fns/locale/ru";
import { Checkbox } from "@mui/material";

let dateFales;

function OrdersPage() {
  const [orders, setOrders] = useState([]);
  const [counterparties, setCounterparties] = useState([]);
  const [products, setProducts] = useState([]);

  // Диалог для Excel
  const [openExcelDialog, setOpenExcelDialog] = useState(false);
  const [excelFile, setExcelFile] = useState(null);
  const [excelDate, setExcelDate] = useState("");

  // Для диалога "Добавить заказ"
  const [openDialog, setOpenDialog] = useState(false);
  const [newOrder, setNewOrder] = useState({
    counterparty_id: "",
    order_date: "",
    items: [],
  });

  // Фильтр по дате
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Фильтр ЭДО
  const [edoFilter, setEdoFilter] = useState("all"); // "all", "true", "false"
  const [edoMenuAnchor, setEdoMenuAnchor] = useState(null);

  // Фильтр контрагентов
  const [counterpartyFilter, setCounterpartyFilter] = useState("all"); // "all" или название контрагента
  const [counterpartyKeyFilter, setCounterpartyKeyFilter] = useState("all"); // "all" или ключ контрагента
  const [counterpartyMenuAnchor, setCounterpartyMenuAnchor] = useState(null);
  const [counterpartyKeyMenuAnchor, setCounterpartyKeyMenuAnchor] =
    useState(null);

  useEffect(() => {
    loadOrders();
    loadCounterparties();
    loadProducts();
  }, []);

  // useEffect для перезагрузки заказов при изменении фильтров
  useEffect(() => {
    loadOrders();
  }, [
    startDate,
    endDate,
    edoFilter,
    counterpartyFilter,
    counterpartyKeyFilter,
  ]);

  const loadOrders = async () => {
    try {
      const params = {};
      if (startDate) {
        params.start = startDate
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }
      if (endDate) {
        params.end = endDate
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }

      // Добавляем фильтр ЭДО
      if (edoFilter !== "all") {
        params.edo_sent = edoFilter;
      }

      // Добавляем фильтр контрагентов
      if (counterpartyKeyFilter !== "all") {
        // Фильтр по ключу имеет приоритет
        params.counterparty_key = counterpartyKeyFilter;
      } else if (counterpartyFilter !== "all") {
        params.counterparty_name = counterpartyFilter;
      }

      // if (startDate) params.start = startDate.toISOString().split("T")[0];
      // if (endDate) params.end = endDate.toISOString().split("T")[0];

      const res = await api.get("/orders", { params });
      setOrders(res.data);
    } catch (err) {
      console.error("Failed to fetch orders:", err);
    }
  };

  const loadCounterparties = async () => {
    try {
      const res = await api.get("/counterparties");
      setCounterparties(res.data);
    } catch (err) {
      console.error("Failed to fetch counterparties:", err);
    }
  };

  const loadProducts = async () => {
    try {
      const res = await api.get("/products");
      setProducts(res.data);
    } catch (err) {
      console.error("Failed to fetch products:", err);
    }
  };

  // Открыть диалог
  const handleOpenDialog = () => {
    setNewOrder({
      counterparty_id: "",
      order_date: "",
      items: [],
    });
    setOpenDialog(true);
  };
  const handleCloseDialog = () => setOpenDialog(false);

  // Добавить строку "item" в заказ
  const handleAddItem = () => {
    setNewOrder((prev) => ({
      ...prev,
      items: [...prev.items, { product_id: "", quantity: 1 }],
    }));
  };

  // Удалить строку item
  const handleRemoveItem = (index) => {
    setNewOrder((prev) => {
      const newItems = [...prev.items];
      newItems.splice(index, 1);
      return { ...prev, items: newItems };
    });
  };

  // Изменение полей
  const handleChangeItem = (index, field, value) => {
    setNewOrder((prev) => {
      const newItems = [...prev.items];
      newItems[index][field] = value;
      return { ...prev, items: newItems };
    });
  };

  // Сохранение заказа
  const handleCreateOrder = async () => {
    try {
      // Если не указана дата, возьмём today
      let dateToUse;

      if (!newOrder.order_date) {
        dateToUse = new Date();
      } else {
        dateToUse = newOrder.order_date
          .toLocaleDateString("ru-RU", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
          .split(".")
          .reverse()
          .join("-"); // Преобразование в формат YYYY-MM-DD
      }

      // Готовим payload
      const payload = {
        counterparty_id: Number(newOrder.counterparty_id),
        order_date: dateToUse,
        items: newOrder.items.map((i) => ({
          product_id: Number(i.product_id),
          quantity: Number(i.quantity),
        })),
      };

      await api.post("/orders", payload);
      handleCloseDialog();
      loadOrders();
    } catch (err) {
      console.error("Failed to create order:", err);
    }
  };

  const handleDeleteOrder = async (id) => {
    if (!window.confirm("Удалить заказ?")) return;
    try {
      await api.delete(`/orders/${id}`);
      loadOrders();
    } catch (err) {
      console.error("Failed to delete order:", err);
    }
  };

  // 2) "Посмотреть УПД (HTML)" и "Скачать PDF"
  const viewUPD = (orderId) => {
    // Откроем HTML в новой вкладке
    window.open(`/api/html/order/${orderId}`, "_blank");
  };
  const downloadPDF = (orderId) => {
    // Откроем PDF
    window.open(`/api/pdf/puppeteer/${orderId}`, "_blank");
  };

  // 3) Excel Dialog
  const handleOpenExcelDialog = () => {
    setExcelFile(null);
    setExcelDate("");
    setOpenExcelDialog(true);
  };
  const handleCloseExcelDialog = () => {
    setOpenExcelDialog(false);
  };
  const handleUploadExcel = async () => {
    try {
      if (!excelFile) {
        alert("Выберите файл Excel!");
        return;
      }
      const formData = new FormData();
      formData.append("file", excelFile);
      formData.append("date", excelDate); // Можно передавать дату в теле как текст

      // Отправляем multipart/form-data
      await api.post("/excel/import", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      alert("Импорт успешно завершён!");
      handleCloseExcelDialog();
      loadOrders();
    } catch (err) {
      console.error("Failed to import Excel:", err);
      alert("Ошибка при загрузке Excel");
    }
  };

  const handleToggleEdo = useCallback(async (orderId, checked) => {
    try {
      await api.put(`/orders/${orderId}`, { edo_sent: checked });
      setOrders((prev) =>
        prev.map((o) => (o.id === orderId ? { ...o, edo_sent: checked } : o))
      );
    } catch (err) {
      console.error("Failed to update EDO flag", err);
    }
  }, []);

  // Функции для работы с фильтром ЭДО
  const handleEdoMenuOpen = (event) => {
    setEdoMenuAnchor(event.currentTarget);
  };

  const handleEdoMenuClose = () => {
    setEdoMenuAnchor(null);
  };

  const handleEdoFilterChange = (filterValue) => {
    setEdoFilter(filterValue);
    handleEdoMenuClose();
  };

  const getEdoFilterLabel = () => {
    switch (edoFilter) {
      case "true":
        return "+";
      case "false":
        return "-";
      default:
        return "*";
    }
  };

  // Функции для работы с фильтром контрагентов
  const handleCounterpartyMenuOpen = (event) => {
    setCounterpartyMenuAnchor(event.currentTarget);
  };

  const handleCounterpartyMenuClose = () => {
    setCounterpartyMenuAnchor(null);
  };

  const handleCounterpartyFilterChange = (filterValue) => {
    setCounterpartyFilter(filterValue);
    // При выборе названия контрагента сбрасываем фильтр по ключу
    if (filterValue !== "all" || filterValue == "all") {
      setCounterpartyKeyFilter("all");
    }
    handleCounterpartyMenuClose();
  };

  const getCounterpartyFilterLabel = () => {
    if (counterpartyKeyFilter !== "all") {
      return counterpartyKeyFilter;
    }
    if (counterpartyFilter === "all") {
      return "*";
    }
    return counterpartyFilter;
  };

  // Получаем уникальные названия контрагентов для меню
  const getUniqueCounterpartyNames = () => {
    const names = new Set();
    counterparties.forEach((cp) => {
      if (cp.name) {
        names.add(cp.name);
      }
    });
    return Array.from(names).sort();
  };

  // Получаем ключи для конкретного контрагента
  const getCounterpartyKeys = (counterpartyName) => {
    return counterparties
      .filter((cp) => cp.name === counterpartyName)
      .map((cp) => cp.key)
      .filter((key) => key) // убираем пустые ключи
      .sort();
  };

  // Проверяем, есть ли у контрагента несколько ключей
  const hasMultipleKeys = (counterpartyName) => {
    return getCounterpartyKeys(counterpartyName).length > 1;
  };

  // Функции для работы с фильтром ключей контрагентов
  const handleCounterpartyKeyMenuOpen = (event) => {
    setCounterpartyKeyMenuAnchor(event.currentTarget);
  };

  const handleCounterpartyKeyMenuClose = () => {
    setCounterpartyKeyMenuAnchor(null);
  };

  const handleCounterpartyKeyFilterChange = (filterValue) => {
    setCounterpartyKeyFilter(filterValue);
    // При выборе ключа сбрасываем фильтр по названию
    if (filterValue !== "all") {
      setCounterpartyFilter("all");
    }
    handleCounterpartyKeyMenuClose();
  };

  // Функция для подсчета общего количества товаров в заказе
  const getTotalQuantity = (order) => {
    // Проверяем разные возможные имена полей для позиций заказа
    const items = order.order_items || order.OrderItems || order.items || [];

    if (!Array.isArray(items)) {
      return 0;
    }

    return items.reduce((total, item) => total + (item.quantity || 0), 0);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ruLocale}>
      <div>
        <Typography variant="h5" gutterBottom>
          Заказы
        </Typography>
        {/* Выбор даты */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            mr: 2,
            mb: 2,
            gap: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <DatePicker
              label="С"
              value={startDate}
              onChange={setStartDate}
              renderInput={(params) => <params.TextField {...params} />}
              slotProps={{ textField: { size: "small", sx: { width: 150 } } }} // уменьшить поле
            />
            <DatePicker
              label="По"
              value={endDate}
              onChange={setEndDate}
              renderInput={(params) => <params.TextField {...params} />}
              slotProps={{ textField: { size: "small", sx: { width: 150 } } }} // уменьшить поле
            />
            <Button variant="contained" onClick={loadOrders}>
              Применить
            </Button>
            <Button
              variant="outlined"
              onClick={() => {
                setStartDate(null);
                setEndDate(null);
                loadOrders();
              }}
            >
              Сбросить
            </Button>
          </Box>
        </Box>

        {/* Кнопки сверху */}
        <Button
          variant="contained"
          onClick={() => handleOpenDialog()}
          sx={{ mr: 2, mb: 2 }}
        >
          Добавить заказ
        </Button>
        <Button
          variant="outlined"
          onClick={handleOpenExcelDialog}
          sx={{ mb: 2 }}
        >
          Загрузить Excel
        </Button>

        {/* Таблица заказов */}
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  ЭДО ({getEdoFilterLabel()})
                  <IconButton
                    size="small"
                    onClick={handleEdoMenuOpen}
                    sx={{ padding: 0.5 }}
                  >
                    <SettingsIcon fontSize="small" />
                  </IconButton>
                </Box>
              </TableCell>
              <TableCell>Номер заказа</TableCell>
              <TableCell>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  Контрагент ({getCounterpartyFilterLabel()})
                  <IconButton
                    size="small"
                    onClick={handleCounterpartyMenuOpen}
                    sx={{ padding: 0.5 }}
                  >
                    <SettingsIcon fontSize="small" />
                  </IconButton>
                </Box>
              </TableCell>
              <TableCell>Дата</TableCell>
              <TableCell>Кол-во</TableCell>
              <TableCell>Сумма</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow
                key={order.id}
                // если edo_sent === true, фон — светло-зелёный
                sx={{
                  backgroundColor: !order.edo_sent
                    ? "rgba(94, 248, 153, 0.1)"
                    : "inherit",
                }}
              >
                <TableCell>
                  <Checkbox
                    checked={order.edo_sent}
                    onChange={(e) =>
                      handleToggleEdo(order.id, e.target.checked)
                    }
                  />
                </TableCell>

                <TableCell>{order.order_number}</TableCell>
                <TableCell>
                  {order.counterparty?.key} {order.counterparty?.name}
                </TableCell>
                <TableCell>{order.order_date}</TableCell>
                <TableCell>{getTotalQuantity(order)}</TableCell>
                <TableCell>{order.total_sum}</TableCell>
                <TableCell>
                  <Button
                    size="small"
                    component={Link}
                    to={`/orders/${order.id}`}
                  >
                    Подробнее
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleDeleteOrder(order.id)}
                  >
                    Удалить
                  </Button>
                  {/* <br /> */}
                  <Button size="small" onClick={() => viewUPD(order.id)}>
                    Посмотреть УПД
                  </Button>
                  <Button size="small" onClick={() => downloadPDF(order.id)}>
                    Скачать PDF
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Меню фильтра ЭДО */}
        <Menu
          anchorEl={edoMenuAnchor}
          open={Boolean(edoMenuAnchor)}
          onClose={handleEdoMenuClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
        >
          <MenuItem onClick={() => handleEdoFilterChange("all")}>
            <ListItemText primary="Все" />
          </MenuItem>
          <MenuItem onClick={() => handleEdoFilterChange("true")}>
            <ListItemText primary="Отмеченные" />
          </MenuItem>
          <MenuItem onClick={() => handleEdoFilterChange("false")}>
            <ListItemText primary="Не отмеченные" />
          </MenuItem>
        </Menu>

        {/* Меню фильтра контрагентов */}
        <Menu
          anchorEl={counterpartyMenuAnchor}
          open={Boolean(counterpartyMenuAnchor)}
          onClose={handleCounterpartyMenuClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
        >
          <MenuItem onClick={() => handleCounterpartyFilterChange("all")}>
            <ListItemText primary="Все" />
          </MenuItem>
          {getUniqueCounterpartyNames().map((name) => (
            <MenuItem
              key={name}
              onClick={() => handleCounterpartyFilterChange(name)}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  width: "100%",
                }}
              >
                <ListItemText primary={name} />
                {hasMultipleKeys(name) && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCounterpartyKeyMenuOpen(e);
                      // Сохраняем выбранное название контрагента для отображения ключей
                      setCounterpartyFilter(name);
                    }}
                    sx={{ padding: 0.5 }}
                  >
                    <SettingsIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            </MenuItem>
          ))}
        </Menu>

        {/* Меню фильтра ключей контрагентов */}
        <Menu
          anchorEl={counterpartyKeyMenuAnchor}
          open={Boolean(counterpartyKeyMenuAnchor)}
          onClose={handleCounterpartyKeyMenuClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
        >
          <MenuItem onClick={() => handleCounterpartyKeyFilterChange("all")}>
            <ListItemText primary="Все ключи" />
          </MenuItem>
          {counterpartyFilter !== "all" &&
            getCounterpartyKeys(counterpartyFilter).map((key) => (
              <MenuItem
                key={key}
                onClick={() => handleCounterpartyKeyFilterChange(key)}
              >
                <ListItemText primary={key} />
              </MenuItem>
            ))}
        </Menu>

        {/* Диалог создания заказа */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>Добавить заказ</DialogTitle>
          <DialogContent>
            <Select
              fullWidth
              sx={{ mt: 2 }}
              displayEmpty
              value={newOrder.counterparty_id}
              onChange={(e) =>
                setNewOrder({ ...newOrder, counterparty_id: e.target.value })
              }
            >
              <MenuItem value="">Выберите контрагента</MenuItem>
              {counterparties.map((ct) => (
                <MenuItem key={ct.id} value={ct.id}>
                  {ct.key} {ct.name} {ct.address}
                </MenuItem>
              ))}
            </Select>
            
            <DatePicker
              label="Дата заказа (YYYY-MM-DD)"
              fullWidth
              margin="normal"
              value={null}
              onChange={(e) => setNewOrder({ ...newOrder, order_date: e })}
              sx={{ display: "flex", mt: 1 }}
              //renderInput={(params) => <div {...params} />}
            />

            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddItem}
              sx={{ display: "flex", mt: 1 }}
            >
              Добавить позицию
            </Button>

            {/* Таблица позиций */}
            {newOrder.items.map((item, idx) => (
              <div
                key={idx}
                style={{ display: "flex", alignItems: "center", marginTop: 10 }}
              >
                <Select
                  sx={{ mr: 1, minWidth: 400 }}
                  displayEmpty
                  value={item.product_id}
                  onChange={(e) =>
                    handleChangeItem(idx, "product_id", e.target.value)
                  }
                >
                  <MenuItem value="">Товар</MenuItem>
                  {products.map((p) => (
                    <MenuItem key={p.id} value={p.id}>
                      {p.name}
                    </MenuItem>
                  ))}
                </Select>

                <TextField
                  label="Количество"
                  type="number"
                  sx={{ width: 100, mr: 1 }}
                  value={item.quantity}
                  onChange={(e) =>
                    handleChangeItem(idx, "quantity", e.target.value)
                  }
                />

                <IconButton color="error" onClick={() => handleRemoveItem(idx)}>
                  <DeleteIcon />
                </IconButton>
              </div>
            ))}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Отмена</Button>
            <Button variant="contained" onClick={handleCreateOrder}>
              Сохранить
            </Button>
          </DialogActions>
        </Dialog>

        {/* Диалог Загрузки Excel */}
        <Dialog open={openExcelDialog} onClose={handleCloseExcelDialog}>
          <DialogTitle>Загрузить Excel</DialogTitle>
          <DialogContent>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Выберите Excel-файл и дату, которая будет использоваться при
              импорте
            </Typography>
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={(e) => setExcelFile(e.target.files[0])}
            />
            {/* <TextField
              label="Дата (YYYY-MM-DD)"
              fullWidth
              margin="normal"
              value={excelDate}
              onChange={e => setExcelDate(e.target.value)}
            /> */}
            <DatePicker
              label="Дата заказа (YYYY-MM-DD)"
              fullWidth
              margin="normal"
              value={null}
              onChange={(e) =>
                setExcelDate(
                  e
                    .toLocaleDateString("ru-RU", {
                      year: "numeric",
                      month: "2-digit",
                      day: "2-digit",
                    })
                    .split(".")
                    .reverse()
                    .join("-")
                )
              }
              sx={{ display: "flex", mt: 1 }}
              //renderInput={(params) => <div {...params} />}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseExcelDialog}>Отмена</Button>
            <Button variant="contained" onClick={handleUploadExcel}>
              Загрузить
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </LocalizationProvider>
  );
}

export default OrdersPage;
