// server/controllers/aktSverki.controller.js
const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');
const { Order, OrderItem, Product, Counterparty, IpInfo, Payment } = require('../models/associations');
const { Op } = require('sequelize');

async function generateAktSverkiHTML(counterpartyName, startDate, endDate) {
  // 1) Находим все заказы контрагента за период
  const ordersInPeriod = await Order.findAll({
    where: {
      order_date: {
        [Op.between]: [startDate, endDate]
      }
    },
    include: [
      {
        model: Counterparty,
        where: {
          name: {
            [Op.iLike]: `%${counterpartyName}%`
          }
        }
      },
      { model: OrderItem, include: [Product] }
    ],
    order: [['order_date', 'ASC']]
  });

  // 2) Находим все оплаты контрагента за период
  const paymentsInPeriod = await Payment.findAll({
    where: {
      payment_date: {
        [Op.between]: [startDate, endDate]
      },
      counterparty_name: {
        [Op.iLike]: `%${counterpartyName}%`
      }
    },
    order: [['payment_date', 'ASC']]
  });

  // 3) Находим все заказы контрагента до начала периода
  const ordersBeforePeriod = await Order.findAll({
    where: {
      order_date: {
        [Op.lt]: startDate
      }
    },
    include: [
      {
        model: Counterparty,
        where: {
          name: {
            [Op.iLike]: `%${counterpartyName}%`
          }
        }
      }
    ]
  });

  // 4) Находим все оплаты контрагента до начала периода
  const paymentsBeforePeriod = await Payment.findAll({
    where: {
      payment_date: {
        [Op.lt]: startDate
      },
      counterparty_name: {
        [Op.iLike]: `%${counterpartyName}%`
      }
    }
  });

  // 5) Получаем информацию об ИП
  const ipInfo = await IpInfo.findOne();

  // 6) Вычисляем суммы
  const totalOrdersBeforePeriod = ordersBeforePeriod.reduce((sum, order) => sum + parseFloat(order.total_sum || 0), 0);
  const totalPaymentsBeforePeriod = paymentsBeforePeriod.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);

  const turnover_debit = ordersInPeriod.reduce((sum, order) => sum + parseFloat(order.total_sum || 0), 0);
  const turnover_credit = paymentsInPeriod.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);

  // 7) Вычисляем начальные остатки
  const startBalance = totalOrdersBeforePeriod - totalPaymentsBeforePeriod;
  const debit_start_balance = startBalance > 0 ? startBalance : 0;
  const credit_start_balance = startBalance < 0 ? Math.abs(startBalance) : 0;

  // 8) Вычисляем конечные остатки
  const endBalance = debit_start_balance - credit_start_balance + turnover_debit - turnover_credit;
  const debit_closing_balance = endBalance > 0 ? endBalance : 0;
  const credit_closing_balance = endBalance < 0 ? Math.abs(endBalance) : 0;

  // 9) Определяем в чью пользу задолженность
  let party_amount = '';
  let amount = 0;
  if (debit_closing_balance > 0) {
    party_amount = ipInfo?.org_name || 'ИП';;
    amount = debit_closing_balance;
  } else if (credit_closing_balance > 0) {
    party_amount = counterpartyName;
    amount = credit_closing_balance;
  }

  // 10) Объединяем и сортируем операции по дате
  const operations = [];

  ordersInPeriod.forEach(order => {
    operations.push({
      dateRaw: order.order_date,
      date: formatDDMMYYYY(order.order_date),
      number: order.order_number,
      debit: parseFloat(order.total_sum || 0).toLocaleString('ru-RU', { minimumFractionDigits: 2 }),
      credit: 0,
      type: 'order'
    });
  });

  paymentsInPeriod.forEach(payment => {
    operations.push({
      dateRaw: payment.payment_date,
      date: formatDDMMYYYY(payment.payment_date),
      number: payment.payment_number,
      debit: 0,
      credit: parseFloat(payment.amount || 0).toLocaleString('ru-RU', { minimumFractionDigits: 2 }),
      type: 'payment'
    });
  });

  // Сортируем по дате
  operations.sort((a, b) => new Date(a.dateRaw) - new Date(b.dateRaw));

  // 11) Готовим данные для шаблона
  const templateData = {
    period_start: formatDDMMYYYY(startDate),
    period_end: formatDDMMYYYY(endDate),
    party1_fullname: ipInfo?.org_name || 'ИП',
    party2_fullname: counterpartyName,
    contract_name: 'основному', // можно сделать динамическим
    operations: operations,
    turnover_debit: turnover_debit.toLocaleString('ru-RU', { minimumFractionDigits: 2 }),
    turnover_credit: turnover_credit.toLocaleString('ru-RU', { minimumFractionDigits: 2 }),
    debit_start_balance: debit_start_balance.toFixed(2),
    credit_start_balance: credit_start_balance.toFixed(2),
    debit_closing_balance: debit_closing_balance.toFixed(2),
    credit_closing_balance: credit_closing_balance.toFixed(2),
    party_amount: party_amount,
    amount: amount.toFixed(2)
  };

  // 12) Регистрируем helper для Handlebars
  Handlebars.registerHelper('eq', function (a, b) {
    return a === b;
  });

  // 13) Читаем и компилируем шаблон
  const templatePath = path.join(__dirname, '../templates/akt_sverki.html');
  const templateSource = fs.readFileSync(templatePath, 'utf8');
  const template = Handlebars.compile(templateSource);

  // 14) Генерируем HTML
  const html = template(templateData);
  return html;
}

module.exports = {
  generateAktSverkiHTML
};

function formatDDMMYYYY(isoDate) {
  const [year, month, day] = isoDate.split('-');
  return `${day}.${month}.${year}`;
}