// src/pages/OrderCreatePage.jsx
import React, { useEffect, useState } from "react";
import api from "../services/api";
import { useNavigate } from "react-router-dom";

function OrderCreatePage() {
  const navigate = useNavigate();
  const [counterparties, setCounterparties] = useState([]);
  const [products, setProducts] = useState([]);

  const [selectedCounterparty, setSelectedCounterparty] = useState("");
  const [orderNumber, setOrderNumber] = useState("");
  const [items, setItems] = useState([]);
  // items = [{productId: X, quantity: Y}, ...]

  // Загружаем контрагентов и товары
  useEffect(() => {
    const loadData = async () => {
      const [ctRes, prRes] = await Promise.all([
        api.get("/counterparties"),
        api.get("/products"),
      ]);
      setCounterparties(ctRes.data);
      setProducts(prRes.data);
    };
    loadData();
  }, []);

  const addItem = () => {
    setItems([...items, { productId: "", quantity: 1 }]);
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    setItems(newItems);
  };

  const createOrder = async () => {
    try {
      // Упакуем items
      const payloadItems = items.map((it) => ({
        product_id: Number(it.productId),
        quantity: Number(it.quantity),
      }));

      const payload = {
        counterparty_id: Number(selectedCounterparty),
        order_date: new Date().toISOString().split("T")[0],
        items: payloadItems,
      };

      // Добавляем номер заказа, если указан
      if (orderNumber.trim()) {
        payload.order_number = orderNumber.trim();
      }

      const res = await api.post("/orders", payload);
      console.log("Order created", res.data);
      // переходим на страницу списка заказов
      navigate("/orders");
    } catch (error) {
      console.error("Failed to create order:", error);
    }
  };

  return (
    <div>
      <h2>Создать заказ</h2>
      <div>
        <label>Контрагент:</label>
        <select
          value={selectedCounterparty}
          onChange={(e) => setSelectedCounterparty(e.target.value)}
        >
          <option value="">Выбрать</option>
          {counterparties.map((ct) => (
            <option key={ct.id} value={ct.id}>
              {ct.key} {ct.name}, {ct.address}
            </option>
          ))}
        </select>
      </div>

      <div style={{ marginTop: "1rem" }}>
        <label>Номер заказа (необязательно):</label>
        <input
          type="text"
          value={orderNumber}
          onChange={(e) => setOrderNumber(e.target.value)}
          placeholder="Оставьте пустым для автогенерации"
          style={{ marginLeft: "0.5rem", padding: "0.25rem" }}
        />
      </div>

      <div style={{ marginTop: "1rem" }}>
        <button onClick={addItem}>Добавить позицию</button>
      </div>

      <table style={{ marginTop: "1rem" }}>
        <thead>
          <tr>
            <th>Товар</th>
            <th>Количество</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, idx) => (
            <tr key={idx}>
              <td>
                <select
                  value={item.productId}
                  onChange={(e) =>
                    handleItemChange(idx, "productId", e.target.value)
                  }
                >
                  <option value="">Выбрать товар</option>
                  {products.map((p) => (
                    <option key={p.id} value={p.id}>
                      {p.name}
                    </option>
                  ))}
                </select>
              </td>
              <td>
                <input
                  type="number"
                  value={item.quantity}
                  onChange={(e) =>
                    handleItemChange(idx, "quantity", e.target.value)
                  }
                  min="1"
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <button style={{ marginTop: "1rem" }} onClick={createOrder}>
        Сохранить
      </button>
    </div>
  );
}

export default OrderCreatePage;
