// server/models/associations.js
const IpInfo = require('./ipInfo.model');
const Counterparty = require('./counterparties.model');
const Product = require('./products.model');
const Order = require('./orders.model');
const OrderItem = require('./orderItems.model');
const User = require('./user.model');
const Payment = require('./payments.model');

// Пример: заказы привязаны к контрагенту
Counterparty.hasMany(Order, {
  foreignKey: 'counterparty_id',
  onDelete: 'CASCADE',
});
Order.belongsTo(Counterparty, {
  foreignKey: 'counterparty_id',
});

// Связь: Order -> OrderItem -> Product
Order.hasMany(OrderItem, {
  foreignKey: 'order_id',
  onDelete: 'CASCADE',
});
OrderItem.belongsTo(Order, {
  foreignKey: 'order_id',
});

Product.hasMany(OrderItem, {
  foreignKey: 'product_id',
  onDelete: 'CASCADE',
});
OrderItem.belongsTo(Product, {
  foreignKey: 'product_id',
});


module.exports = {
  IpInfo,
  Counterparty,
  Product,
  Order,
  OrderItem,
  User,
  Payment
};
