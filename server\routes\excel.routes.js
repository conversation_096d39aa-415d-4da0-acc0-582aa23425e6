const { Router } = require('express');
const router = Router();
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() }); // сохраняем в память для обработки

const { importOrdersFromExcel } = require('../controllers/excel.controller');
const { importPaymentsFromExcel } = require('../controllers/paymentsExcel.controller');

// POST /api/excel/import
router.post('/import', upload.single('file'), importOrdersFromExcel);

// POST /api/excel/payments-import
router.post('/payments-import', upload.single('file'), importPaymentsFromExcel);

module.exports = router;
