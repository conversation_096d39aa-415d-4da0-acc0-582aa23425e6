// server/controllers/aktSverkiPreview.controller.js
const { generateAktSverkiHTML } = require('./aktSverki.controller');

module.exports = {
  // GET /api/html/akt-sverki?counterparty_name=...&start_date=...&end_date=...
  viewAktSverkiHTML: async (req, res) => {
    try {
      const { counterparty_name, start_date, end_date } = req.query;

      if (!counterparty_name || !start_date || !end_date) {
        return res.status(400).json({ 
          error: 'counterparty_name, start_date and end_date are required' 
        });
      }

      const html = await generateAktSverkiHTML(counterparty_name, start_date, end_date);
      
      // Возвращаем HTML как есть
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.send(html);
    } catch (error) {
      console.error('Error generating Akt Sverki HTML:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
};
