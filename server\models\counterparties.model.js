// server/models/counterparties.model.js
const { DataTypes } = require('sequelize');
const sequelize = require('../config/db');

const Counterparty = sequelize.define('counterparties', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  inn: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  kpp: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  address: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  legal_address: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  contract: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  key: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  note: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
}, {
  tableName: 'counterparties',
  timestamps: false,
});

module.exports = Counterparty;
