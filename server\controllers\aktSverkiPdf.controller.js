// server/controllers/aktSverkiPdf.controller.js
const puppeteer = require('puppeteer');
const { generateAktSverkiHTML } = require('./aktSverki.controller');

module.exports = {
  // GET /api/pdf/akt-sverki?counterparty_name=...&start_date=...&end_date=...
  generateAktSverkiPDF: async (req, res) => {
    try {
      const { counterparty_name, start_date, end_date } = req.query;

      if (!counterparty_name || !start_date || !end_date) {
        return res.status(400).json({
          error: 'counterparty_name, start_date and end_date are required'
        });
      }

      // 1) Генерируем HTML
      const html = await generateAktSverkiHTML(counterparty_name, start_date, end_date);

      // 2) Запускаем Puppeteer
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();

      // 3) Загружаем HTML
      await page.setContent(html, { waitUntil: 'networkidle0' });

      // 4) Генерируем PDF
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '10mm',
          bottom: '20mm',
          left: '15mm'
        },
        scale: 0.65
      });

      await browser.close();

      // 5) Отправляем PDF
      // const filename = `akt_sverki_ooodvnevada_${start_date}_${end_date}.pdf`;
      // res.setHeader('Content-Type', 'application/pdf');
      // res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      // res.send(pdfBuffer);

      // 1) сперва готовим ASCII‑версии (для старых браузеров/клиентов):
      const asciiName = counterparty_name
        .replace(/[^a-z0-9_-]/gi, '_');      // всё, что не латиница/цифры/нижнее подчёркивание/дефис, заменяем на '_'
      const asciiStart = start_date.replace(/[^0-9-]/g, '_');  // оставляем только цифры и дефис
      const asciiEnd = end_date.replace(/[^0-9-]/g, '_');

      const asciiFilename = `akt_sverki_${asciiName}_${asciiStart}_${asciiEnd}.pdf`;

      // 2) а теперь оригинал—URL‑кодируем для современного клиента:
      const encodedFilename = encodeURIComponent(
        `akt_sverki_${counterparty_name}_${start_date}_${end_date}.pdf`
      );

      // 3) склеиваем заголовок:
      const disposition =
        `attachment; filename="${asciiFilename}"; ` +
        `filename*=UTF-8''${encodedFilename}`;

      // 5) Отдаем бинарник
      res
        .status(200)
        .set({
          'Content-Type': 'application/pdf',
          'Content-Disposition': disposition,
          'Content-Length': pdfBuffer.length,
          'Content-Transfer-Encoding': 'binary',
          'Accept-Ranges': 'bytes'
        })
        .end(pdfBuffer, 'binary');
      // res.send(pdfBuffer);

    } catch (error) {
      console.error('Error generating Akt Sverki PDF:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
};
